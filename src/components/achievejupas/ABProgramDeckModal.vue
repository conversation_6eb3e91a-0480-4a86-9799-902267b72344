<template>
  <ion-header>
    <!-- Mainly for AB3 -->
    <ion-toolbar v-if="isAB3 || !singleSelectMode">
      <!-- Back buttons (back / close modal) -->
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="confirmSelect(true)"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
      </ion-buttons>

      <ion-title style="padding-left: 16px">
        <ion-label class="ion-text-wrap">
          <h2 v-if="isAB3">
            <b><span style="vertical-align: middle"><ion-icon :icon="thumbsUpOutline"></ion-icon></span>
            &nbsp;2+ programs</b>
          </h2>
          <h2 v-else>
            <b><span style="vertical-align: middle"><ion-icon :icon="thumbsUpOutline"></ion-icon></span>
            &nbsp;20 programs</b>
          </h2>
          <p v-if="targetDiscipline">for {{ targetDiscipline?.name }}</p>
          <p v-else-if="!isAB3">{{ chosenPrograms.length }}/20 selected</p>
        </ion-label>
      </ion-title>

      <ion-buttons slot="end" v-if="takenActions">
        <ion-button fill="solid" color="success" @click="confirmSelect()">
          Save
          <ion-icon slot="end" :icon="checkmark"></ion-icon>
        </ion-button>
      </ion-buttons>
    </ion-toolbar>

    <!-- AchieveJUPAS header -->
    <ion-toolbar style="--min-height: 24px" v-else>
      <!-- Back buttons (back / close modal) -->
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="confirmSelect(true)"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
      </ion-buttons>

      <ion-button  expand="block" color="medium" size="small" @click.stop="undefined" :href="loggedInUser?.waGroupLink" target="_blank">
        <ion-icon slot="start" :icon="flagOutline"></ion-icon>
        有唔妥？即時cap圖/錄音俾我哋！
        <ion-icon slot="end" :icon="logoWhatsapp"></ion-icon>
      </ion-button>
    </ion-toolbar>
  </ion-header>

  <ion-content :fullscreen="true">
    <!-- Searchbar Input -->
    <ion-toolbar v-show="isSearching">
      <ion-searchbar id="keyword-searchbar" style="padding-bottom: 1px" mode="md" v-model="searchKeyword" :placeholder="t('search')"
                    @ionFocus="isSearching = true" @ionCancel="isSearching = false; selectedFilterGroup = 'disciplines'"
                    @keyup.enter="(e) => e.target.blur()" show-cancel-button="always"></ion-searchbar>
    </ion-toolbar>

    <!--
      Choose Programs (Cards)
      -->
    <div style="height: 100%">
      <swiper
          class="program-slides"
          id="card-swiper-slides"
          :grabCursor="true"
          :navigation="true"
          :modules="modules"
          :virtual="{ enabled: true }"
          v-if="!delayLoading && filteredPrograms.length > 0"
      >
        <swiper-slide class="program-slide" v-for="program in filteredPrograms" :key="program.id" :data-program-id="program.id">
          <!-- Upper Section: Program Card: Name / Image / Website Button-->
          <div class="program-slide-upper-section" :class="{ 'highlighted-border': isSelected(program) }"
              :style="{ 'background-color': `var(--ion-color-${getColorForScore(program)}` }">
            <!--<div style="width: 100%; height: 100%" v-if="program.cardSlideImgLink && selectedOption == 'highlight'">
              <img style="width: 100%; height: 100%; object-fit: contain" :src="getProxyImgLink(program.cardSlideImgLink)"
                    @load="program.loadedCardSlideImg = true" />
              <ion-spinner style="position: absolute; top: 50%; left: 50%" v-if="!program.loadedCardSlideImg"></ion-spinner>

              <div class="ion-text-center bottom-badge" style="width: 100%" v-if="program.jupasUrl || program.programWebsite">
                <program-info-stats :program="program" :show-names="false" :targetUser="targetUser" />
              </div>
            </div>-->
            <!--<ion-row style="width: 100%" v-if="!(program.cardSlideImgLink && program.loadedCardSlideImg) || selectedOption != 'highlight'">-->

            <!-- Program statistics & past admission scores -->
            <div style="width: 100%">
              <program-info-stats :transparent="true" :program="program" :showProgramAdmissionData="true" :showNames="false" :targetUser="targetUser" />
            </div>

            <!-- My weighted scores -->
            <div style="width: 100%; margin-bottom: 8px">
              <SubjectScoreInputSection :title="targetUser ? 'Student Prediction' : 'My Prediction'" source="overallTarget" :targetUser="targetUser" :program="program"
                                        :readonly="!!targetUser" :fromDeckView="true" :hidePredictedScoreInputs="true"></SubjectScoreInputSection>
            </div>

            <program-info-stats :program="program" :showProgramAdmissionData="false" :showNames="false" :targetUser="targetUser" />

            <!-- Program Buttons -->
            <ProgramItemContent :hideNames="true" :item="program" :fromDeckView="true" :hideDisciplineBadges="true" :showRecommendedEvents="false"></ProgramItemContent>

            <ion-item style="--background: transparent; margin-top: 2px">
              <!-- Like Button -->
              <div style="margin-right: 4px" v-if="!readonly">
                <ion-fab-button style="margin: 0 auto" size="small" color="primary" v-if="singleSelectMode && isSelected(program) && program.id != showFirstProgramId">
                  {{ getBandLabel(chosenPrograms.findIndex(p => p.id == program.id)) }}
                </ion-fab-button>
                <ion-fab-button style="margin: 0 auto" size="small" color="primary" v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onThumbsUp(program) } }"
                                :disabled="!passProgramElectiveRequirements(program)" v-else>
                  <ion-icon :icon="isSelected(program) ? thumbsUp : thumbsUpOutline"></ion-icon>
                </ion-fab-button>
              </div>

              <ion-label class="ion-text-center">
                {{ program?.jupasCode }}
                <small style="margin: 0; font-size: 0.8em; line-height: 1.2">
                  <span>{{ program?.label }}</span>
                </small>
              </ion-label>

              <!-- Placeholder (make program name center) -->
              <ion-buttons><ion-button><ion-icon slot="icon-only" :icon="undefined"></ion-icon></ion-button></ion-buttons>
            </ion-item>
          
              <!-- Row: extra info (recommended events, disciplines, etc.) 
              <ion-col size="12"><ProgramItemContent :item="program" :hideNames="true" :fromDeckView="true" :showRecommendedEvents="program.id == 161"></ProgramItemContent></ion-col>
              -->
          </div>
        </swiper-slide>
      </swiper>
    </div>

    <!-- Program Report Modal -->
    <ion-modal :is-open="showReportModal" @didDismiss="showReportModal = false">
      <program-issue-report-modal :program="selectedProgramForReport" v-if="selectedProgramForReport"></program-issue-report-modal>
    </ion-modal>
  </ion-content>

  <!--
    Chip Filter Panel
    -->
  <ion-footer v-show="!isSearching">
    <!-- Materials by Type -->
    <ion-toolbar class="material-div" style="height: auto; padding-bottom: 8px">
      <!-- My Choices -->
      <ion-row v-show="selectedFilterGroup == 'myChoices'">
        <ion-chip @click="selectedOption = 'dislike'" :class="{ 'active-tag': selectedOption == 'dislike' }">
          <ion-icon :color="selectedOption == 'dislike' ? 'light' : 'dark'" style="margin-inline-start: 0" :icon="thumbsDownOutline"></ion-icon>
        </ion-chip>
        <ion-chip @click="selectedOption = 'like'" :class="{ 'active-tag': selectedOption == 'like' }">
          <ion-icon :color="selectedOption == 'like' ? 'light' : 'dark'" style="margin-inline-start: 0" :icon="thumbsUpOutline"></ion-icon>
        </ion-chip>
      </ion-row>

      <!-- Disiciplines -->
      <ion-row v-show="selectedFilterGroup == 'disciplines'">
        <!-- Client Advertisement: Banner -->
        <div style="width: 100%" v-if="user.isAdmin && relatedAdvBanner">
          <div class="ad-banner" v-if="!relatedAdvBanner.bannerImgLink">
            Your department's advertisement
          </div>
          <div class="ad-banner" v-else>
            <img :src="getProxyImgLink(relatedAdvBanner.bannerImgLink)" />
          </div>
        </div>

        <!-- 2nd level: Disciplines -->
        <div v-if="selectedOption.id" style="margin-bottom: 4px; max-height: 10vh; overflow-y: scroll">
          <ion-chip outline v-for="dis in disciplines.filter(d => d.disciplineGroupId == selectedOption.id)" :key="dis.id" @click="nestedOption = dis.id" :class="{ 'active-tag': nestedOption == dis.id }"
                    v-show="numOfAchievableProgramsByDiscipline(dis.id) > 0">
            <ion-label>{{ dis.name }} ({{ numOfAchievableProgramsByDiscipline(dis.id) }})</ion-label>
          </ion-chip>
        </div>

        <!-- 1st level: Highlight (client programs) / Discipline Groups -->
        <div style="overflow-x: auto; white-space: nowrap; padding-bottom: 4px;">
          <ion-chip @click="selectedOption = 'highlight'"
                    :class="{ 'active-tag': selectedOption == 'highlight' }"
                    v-if="!isAB3"
                    style="display: inline-block; margin-right: 4px;">
            <ion-label>Highlight</ion-label>
          </ion-chip>
          <ion-chip v-for="dg in disciplineGroups" :key="dg.id"
                    @click="selectedOption = dg"
                    :class="{ 'active-tag': selectedOption.id == dg.id }"
                    style="display: inline-block; margin-right: 4px;">
            <ion-label>{{ dg.name }} {{ dg.nameChi }} ({{ numOfAchievableProgramsByDisciplineGroup(dg.id) }})</ion-label>
          </ion-chip>
        </div>
      </ion-row>

      <!-- Institutions -->
      <ion-row v-show="selectedFilterGroup == 'institutions'">
        <!-- 2nd level: discipline group -->
        <div style="margin-bottom: 4px; max-height: 10vh; overflow-y: scroll">
          <ion-chip outline v-for="dg in disciplineGroups" :key="dg.id" @click="nestedOption = dg.id" :class="{ 'active-tag': nestedOption == dg.id }"
                    v-show="numOfAchievableProgramsByDisciplineGroup(dg.id) > 0">
            <ion-label>{{ dg.name }} {{ dg.nameChi }} ({{ numOfAchievableProgramsByDisciplineGroup(dg.id) }})</ion-label>
          </ion-chip>
        </div>

        <!-- 1st level: institution -->
        <div style="overflow-x: auto; white-space: nowrap; padding-bottom: 4px;">
          <ion-chip v-for="inst in relatedInstitutions" :key="inst.id"
                    @click="selectedOption = inst"
                    :class="{ 'active-tag': selectedOption.id == inst.id }"
                    style="display: inline-block; margin-right: 4px;">
            <ion-label>{{ inst.nameShort }} ({{ numOfAchievableProgramsByInstitution(inst.id) }})</ion-label>
          </ion-chip>
        </div>
      </ion-row>
    </ion-toolbar>

    <!-- Filter Groups (Disciplines / Institutions / Search) -->
    <ion-toolbar style="--min-height: 24px">
      <ion-segment class="filter-group-segment" mode="ios" v-model="selectedFilterGroup" scrollable>
        <ion-segment-button value="disciplines" v-if="!targetDiscipline">
          <ion-label>Disciplines</ion-label>
        </ion-segment-button>
        <ion-segment-button value="institutions">
          <ion-label class="ion-text-wrap">Institutions</ion-label>
        </ion-segment-button>
        <ion-segment-button value="like" v-if="!singleSelectMode && userPrograms.some(ud => ud.reaction == 'like')">
          <ion-icon size="small" :icon="thumbsUpOutline"></ion-icon>
        </ion-segment-button>
        <ion-segment-button value="search">
          <ion-ripple-effect></ion-ripple-effect>
          <ion-icon size="small" :icon="search"></ion-icon>
        </ion-segment-button>
      </ion-segment>
    </ion-toolbar>

    <!-- Filter programs by predicted scores -->
    <ion-toolbar style="margin-top: 0px; --min-height: 36px">
      <ion-segment class="predicted-score-filters" mode="ios" scrollable v-model="selectedPredictScoreFilter">
        <ion-segment-button value="all">All</ion-segment-button>
        <ion-segment-button value="achievableSelfPredict">Achievable<br />(self-predict)</ion-segment-button>
        <ion-segment-button value="achievableSchoolPredict">Achievable<br />(school-predict)</ion-segment-button>
        <ion-segment-button value="achievableSelfPredict+1">Achievable<br />(self-predict + 1)</ion-segment-button>
      </ion-segment>
    </ion-toolbar>

    <ion-toolbar color="primary" class="ion-text-center" style="--min-height: 24px; font-size: 24px"
                  v-if="selectedPredictScoreFilter != 'achievableSelfPredict+1'">
      <b>{{ achievablePrograms.length }}</b>
    </ion-toolbar>
      
    <!-- Subject Score Input -->
    <SubjectScoreInputSection :targetUser="targetUser" :showSubjectNamesOnly="true" :subjectScorePlusOneExtraPrograms="subjectScorePlusOneExtraPrograms"
                              :showScorePlusOneSection="selectedPredictScoreFilter == 'achievableSelfPredict+1'" @plusOneSubjectIdChanged="plusOneSubjectIdChanged"></SubjectScoreInputSection>

    <SubjectScoreInputSection :targetUser="targetUser" :title="targetUser ? 'Student Prediction' : 'My Prediction'" source="overallTarget" :hideSubjectNames="true" :readonly="!!targetUser"
                              :noBottomBorderRadius="true" :noTopBorderRadius="true" :gradeSelectLargeFont="true" :smallTitles="true" :useBlueBackground="true"></SubjectScoreInputSection>

    <SubjectScoreInputSection :targetUser="targetUser" title="School Prediction" source="schoolPredict" :hideSubjectNames="true" :readonly="!targetUser"
                              :noBottomBorderRadius="true" :noTopBorderRadius="true" :gradeSelectLargeFont="true" :smallTitles="true" :useBlueBackground="true"></SubjectScoreInputSection>
  </ion-footer>
</template>

<script setup lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, onMounted, watch, reactive, defineProps, } from 'vue';

// icons
import { add, close, checkmark, arrowUp,  arrowForward, arrowBack, checkbox, trashOutline,
         thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline,
         chevronBack, chevronForward, repeat, search, flagOutline, logoWhatsapp, ellipse, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder, IonProgressBar,
        IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption, IonSpinner, IonListHeader,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonRippleEffect,
        IonNote, IonTextarea, IonFab, IonFabButton, IonBadge, IonInfiniteScroll, IonInfiniteScrollContent, IonModal, IonAvatar,
        IonicSlides, isPlatform, getPlatforms, modalController, loadingController, } from '@ionic/vue';
import ProgramInfoStats from '@/components/achievejupas/ProgramInfoStats.vue';
import ProgramItemContent from '@/components/achievejupas/ProgramItemContent.vue';
import SubjectScoreInputSection from '@/components/achievejupas/SubjectScoreInputSection.vue';
import ProgramIssueReportModal from '@/components/achievejupas/ProgramIssueReportModal.vue';

// Swiper
import 'swiper/swiper.min.css';
import 'swiper/modules/effect-cards/effect-cards.min.css';
import 'swiper/modules/navigation/navigation.min.css';
import '@ionic/vue/css/ionic-swiper.css';
import { Swiper, SwiperSlide } from 'swiper/vue/swiper-vue';
import { EffectCards, Navigation, Virtual, } from 'swiper';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';
import { Institution, Program, Service, UserClaim, UserProgram } from '@/types';

const props = defineProps([
  "isAB3", "isGPT", "targetDiscipline", "prefilledPrograms", "oldUserPrograms",
  "showFirstProgramId", "singleSelectMode",
  "prefilledDisciplines", "oldUserDisciplines",
  "targetUser", "isDemo", "readonly",
])
// methods or filters
const store = useStore();
const { closeModal, doReorder, openModal, navigateMaterialCategories, getProxyImgLink,
        processUserItems, setUserItemReaction, isItemSelected, isItemDisliked, isMobileWeb,
        onThumbsUpItem, onThumbsDownItem, onClickMoreBtn, onClickPrevBtn, animateToFirstCard,
        recordCurrSlideReaction, resetActiveSlide, syncChosenItems, resetFilters, focusKeywordSearchbar,
        openImageModal, presentPrompt, sleep, htmlToPlainText, presentAlert, getBandLabel, getBandClass, openServiceModal,
        isMeetProgramElectiveRequirements, getUserSubjects, isAchievableProgram, getScoreDiff,
        isMeetUniEntranceRequirements, calculateSumWeightedScores, getFormattedGrade, getDSEScore, } = utils();
const { t } = useI18n();

const searchKeyword = ref("");
const isSearching = ref(false);
const delayLoading = ref(true);

// Report Modal
const showReportModal = ref(false);
const selectedProgramForReport = ref(null);
const openReportModal = (program) => {
  selectedProgramForReport.value = program;
  showReportModal.value = true;
};

// Store
const loggedInUser = computed(() => store.state.user);
const user = computed(() => props.targetUser || store.state.user);
const disciplineGroups = computed(() => store.state.allDisciplineGroups);
const disciplines = computed(() => store.state.allDisciplines); // for discipline tab
const relatedInstitutions = ref<Institution[]>([]);
const allPrograms = ref<Program[]>([]);
const chosenPrograms = ref<Program[]>(props.prefilledPrograms || []);
const userPrograms = ref<UserProgram[]>(props.oldUserPrograms || []);
const tmpNewUserPrograms = ref<UserProgram[]>([]); // for storing slideChange reaction
const takenActions = ref(false);

// TODO: Work Events (implanted promotion)
const workServices = computed<Service[]>(() => store.getters.getServicesByTypes(["Work"]));

// client programs
const advertisements = computed(() => store.state.allAdvertisements);

// filters
const selectedFilterGroup = ref('disciplines');
const selectedOption = ref<any>(props.isAB3 ? 'all' : 'highlight');
const nestedOption = ref('all'); // for nested filters for institutions
const settings = computed(() => store.state.settings);

const getAppState = () => ({
  selectedFilterGroup: selectedFilterGroup.value,
  selectedOption: selectedOption.value,
  searchKeyword: searchKeyword.value,
});
const confirmSelect = async (leaveWithoutSave = false) => {
  if (leaveWithoutSave) {
    if (takenActions.value) {
      await presentPrompt(`當前操作未儲存，確認要離開嗎?<br />Are you sure to leave without saving?`, async () => {
        await closeModal({});
      }, "注意 Attention");
    } else {
        await closeModal({});
    }
  } else {
    let newUserItems = processUserItems(chosenPrograms.value, userPrograms.value, tmpNewUserPrograms.value, 'programId', store.state.user.id);
    if (props.targetDiscipline?.id) {
      newUserItems = newUserItems.map(ui => ({ ...ui, disciplineId: props.targetDiscipline.id })); // link to related discipline
    }
    const loading = await loadingController.create({});
    await loading.present();
    await sleep(1);
    loading.dismiss();

    await closeModal({ "chosen": chosenPrograms.value, "userPrograms": newUserItems, }); // return selected program & order here
  }
};

// Helper functions for program reaction
const setReaction = (programId: any, reaction: any, skipIfExists = false) => {
  setUserItemReaction(programId, reaction, 'programId', userPrograms, tmpNewUserPrograms, getAppState(), skipIfExists);
}
const isSelected = (program: any) => (isItemSelected(program, chosenPrograms));
const isDisliked = (program: any) => (isItemDisliked(program, 'programId', userPrograms));

const startIdx = ref(0); // for card slides

const recordActiveSlideReaction = () => {
  setTimeout(() => {
    const slides: any = document.querySelector('#card-swiper-slides');
    if (slides) {
      const programId = slides.swiper.visibleSlides[0]?.dataset['programId'];
    }
  }, 200);
}
const initSlideListener = (reset = false) => {
  setTimeout(() => {
    // Init card slides
    delayLoading.value = false;
    setTimeout(() => {
      const slides: any = document.querySelector('#card-swiper-slides');
      if (slides) {
        slides.swiper.off('slideChange', recordActiveSlideReaction);
        slides.swiper.on('slideChange', recordActiveSlideReaction);
        recordActiveSlideReaction(); // initial slide
      }
    }, 200);
  }, 200);

  if (reset) resetActiveSlide(startIdx, delayLoading, 'programId', userPrograms, tmpNewUserPrograms, getAppState());
}

// INIT
onMounted(() => {
  const { targetDiscipline, showFirstProgramId, } = props;
  if (targetDiscipline) {
    // AB3
    relatedInstitutions.value = store.getters.getInstitutionsByDisciplineId(targetDiscipline.id, true);
    allPrograms.value = store.getters.getProgramsByDisciplineId(targetDiscipline.id, true);
  } else {
    // Mock JUPAS
    relatedInstitutions.value = store.getters.getInstitutionsByProgramType('jupas'); // only JUPAS intitutions
    allPrograms.value = store.getters.getProgramsByType('jupas'); // JUPAS programs
  }
  if (showFirstProgramId) {
    selectedOption.value = 'all';
    allPrograms.value = [
      ...allPrograms.value.filter(p => p.id == showFirstProgramId),
      ...allPrograms.value.filter(p => p.id != showFirstProgramId),
    ]
  }
  syncChosenItems('programId', chosenPrograms, userPrograms, allPrograms.value);
  initSlideListener();
})
watch(selectedOption, () => {
  initSlideListener();
  if (selectedFilterGroup.value == 'disciplines') {
    nestedOption.value = 'all'; // discipline groups changed
  }
})
watch(nestedOption, () => {
  initSlideListener(true);
})
watch(selectedFilterGroup, (currGroup) => {
  if (['like', 'dislike'].includes(currGroup)) {
    selectedOption.value = currGroup;
  }
  else if (currGroup == 'random') {
    resetFilters(selectedFilterGroup, selectedOption);
    allPrograms.value = store.getters.shuffledPrograms;
    animateToFirstCard();
  }
  else if (currGroup == 'search') {
    resetFilters(selectedFilterGroup, selectedOption);
    focusKeywordSearchbar(isSearching);
  }
  else {
    if (currGroup == 'institutions') { // Mock JUPAS: first show highlight programs
      selectedOption.value = relatedInstitutions.value[0];
    }
    else if (currGroup == 'disciplines') {
      selectedOption.value = 'highlight';
      //selectedOption.value = disciplines.value[0];
    }
    nestedOption.value = 'all'; // reset filter
  }
})

const onThumbsUp = (program: any) => {
  if (props.singleSelectMode) {
    return closeModal({ selectedProgram: program }); // choose & leave
  }
  onThumbsUpItem(program, 'programId', chosenPrograms, userPrograms, tmpNewUserPrograms, getAppState())
  takenActions.value = true;
}
const onThumbsDown = (program: any) => {
  onThumbsDownItem(program, 'programId', chosenPrograms, userPrograms, tmpNewUserPrograms, getAppState())
  takenActions.value = true;
}

// Show program by predicted scores
const selectedPredictScoreFilter = ref("all"); // all / self predict / school predict / self predict + 1
const plusOneSubjectId = ref<any>(24); // predicted score + 1 (default is Chinese)

// When subject score + 1
const subjectScorePlusOneExtraPrograms = computed(() => {
  const { allSubjects } = store.state;
  const userObj = props.targetUser || user.value;
  const userSubjects = getUserSubjects(allSubjects, userObj);
  const userSubjectGrades = (userObj.userSubjectGrades || []).filter(usg => usg.source == 'overallTarget'); // only consider self-predict grade

  // Current achievable programs
  const currAchievablePrograms = allPrograms.value.filter(p => isAchievableProgram(p, allSubjects, userSubjects, userSubjectGrades, 'overallTarget')).slice();

  // Get list of extra programs for each subject when grade + 1
  const resObj = {}; // { <subjectId>: [extra programs] }
  for (const usg of userSubjectGrades) {
    const newSubjectGrade = getFormattedGrade(Math.min(7, getDSEScore({}, usg.grade)+1));
    const newUserSubjectGrades = [...userSubjectGrades.filter(x => x.subjectId != usg.subjectId), { ...usg, grade: newSubjectGrade?.toString() }];
    
    const resPrograms = allPrograms.value.filter(p => isAchievableProgram(p, allSubjects, userSubjects, newUserSubjectGrades, 'overallTarget'));
    resObj[usg.subjectId] = resPrograms.filter(np => !currAchievablePrograms.find(p => p.id == np.id)); // extra programs
  }
  return resObj;
})
const plusOneSubjectIdChanged = (newSubjectId) => {
  plusOneSubjectId.value = newSubjectId;
}

// Achievable Programs
const achievablePrograms = computed(() => {
  const { allSubjects } = store.state;
  const userObj = props.targetUser || user.value;
  const userSubjects = getUserSubjects(allSubjects, userObj);
  const userSubjectGrades = userObj.userSubjectGrades || [];
  const filterVal = selectedPredictScoreFilter.value;
  if (filterVal == 'all') return allPrograms.value;
  if (filterVal == 'achievableSelfPredict') return allPrograms.value.filter(p => isAchievableProgram(p, allSubjects, userSubjects, userSubjectGrades, 'overallTarget'));
  if (filterVal == 'achievableSchoolPredict') return allPrograms.value.filter(p => isAchievableProgram(p, allSubjects, userSubjects, userSubjectGrades, 'schoolPredict'));
  if (filterVal == 'achievableSelfPredict+1') {
    if (plusOneSubjectId.value != null) {
      console.log(subjectScorePlusOneExtraPrograms.value);
      return subjectScorePlusOneExtraPrograms.value[plusOneSubjectId.value]; // specific subject (extra programs)
    }
    // All subjects +1 (predicted scores)
    const plusOneUserSubjectGrades = userSubjectGrades.map(usg => ({
      ...usg,
      grade: getFormattedGrade(Math.min(7, getDSEScore({}, usg.grade)+1))?.toString(),
    }));
    return allPrograms.value.filter(p => isAchievableProgram(p, allSubjects, userSubjects, plusOneUserSubjectGrades, 'overallTarget'));
  }
  return allPrograms.value;
});

// Filtered programs (e.g. discipline tags / discipline group tags)
const filteredPrograms = computed(() => {
  if (props.isDemo) {
    //return allPrograms.value.filter(p => [161, 158].includes(Number(p.id)));
    return allPrograms.value.filter(p => [161, 285].includes(Number(p.id)));
  }
  if (searchKeyword.value) {
    return allPrograms.value.filter(p => {
      const targetText = `${p.name.toLowerCase()} ${(p.jupasCode || '').toLowerCase()}`;
      return targetText.includes(searchKeyword.value.toLowerCase().trim());
    });
  }
  if (['like', 'dislike'].includes(selectedOption.value)) {
    return userPrograms.value.filter(ue => ue.reaction == selectedOption.value).map(ue => {
      return allPrograms.value.find(e => e.id == ue.programId);
    });
  }
  let res = achievablePrograms.value; // Set: achievable programs
  if (selectedOption.value == 'highlight') {
    //res = allPrograms.value.filter(p => p.sortOrder != 99999).sort((a,b) => (a.sortOrder-b.sortOrder)); // featured programs
    res = res.filter(p => p.sortOrder != 99999).sort((a,b) => (a.sortOrder-b.sortOrder)); // featured programs
  }
  else if (selectedFilterGroup.value == 'institutions' && selectedOption.value.id) {
    res = res.filter(p => p.institutionId == selectedOption.value.id); // filter by institutions
    if (nestedOption.value != 'all') {
      res = res.filter(p => p.disciplines.some(d => d.disciplineGroupId?.toString() == nestedOption.value)); // further filter by discipline groups
    }
  }
  else if (selectedFilterGroup.value == 'disciplines' && selectedOption.value.id) {
    res = res.filter(p => p.disciplines.some(d => d.disciplineGroupId?.toString() == selectedOption.value.id)); // filter by discipline groups
    if (nestedOption.value != 'all') {
      res = res.filter(p => p.disciplines.some(d => d.id == nestedOption.value)); // further filter by disciplines
    }
  }
  return [
    ...res.filter(d => !isDisliked(d)),
    ...res.filter(d => isDisliked(d)),
  ];
});

// swiper
const modules = [EffectCards, Navigation, Virtual, IonicSlides];

// Program competition
const getCompetitivenessColor = (ratio: any) => {
  if (ratio <= 6) {  // Bottom 35-40% - Less competitive
    return 'success';
  } else if (ratio <= 20) {  // Middle 45-60% - Moderately competitive
    return 'warning';
  } else {  // Top 15-20% - Highly competitive
    return 'danger';
  }
}

// Program Elective Requirements
const passProgramElectiveRequirements = (program) => {
  const { allSubjects } = store.state;
  const userObj = props.targetUser || user.value;
  const userSubjects = getUserSubjects(allSubjects, userObj);
  const userSubjectGrades = userObj.userSubjectGrades || [];
  return isMeetProgramElectiveRequirements(program, allSubjects, userSubjects, userSubjectGrades, 'overallTarget');
}

// Advertisements (assume 1 discipline / discipline group only 1 client)
const relatedAdvBanner = computed(() => {
  return advertisements.value.find(ad => {
    if (nestedOption.value != 'all') return ad.disciplineId == nestedOption.value; // Discipline
    return ad.disciplineGroupId == selectedOption.value.id; // Discipline Group
  });
})

// Access score component child
const getColorForScore = (program) => {
  const { allSubjects } = store.state;
  const userObj = props.targetUser || user.value;
  const userSubjects = getUserSubjects(allSubjects, userObj);
  const userSubjectGrades = userObj.userSubjectGrades || [];
  const source = 'overallTarget'; // use student target

  const passUniEntranceRequirements = isMeetUniEntranceRequirements(userSubjectGrades.filter(sg => sg.source == source));
  const passProgramElectiveRequirements = isMeetProgramElectiveRequirements(program, allSubjects, userSubjects, userSubjectGrades, source);
  const { notMeetMinGradeSubjects, } = calculateSumWeightedScores(allSubjects, userSubjects, userSubjectGrades, program, source);

  if (notMeetMinGradeSubjects.length > 0 || !passUniEntranceRequirements) return 'fdmtred';
  if (!passProgramElectiveRequirements) return 'medium';
  const scoreDiff = getScoreDiff(program, allSubjects, userSubjects, userSubjectGrades, source);
  if (scoreDiff == null) return 'light';
  if (scoreDiff >= 0) return 'darkgreen';
  return 'fdmtred';
}

// Program count by disciplines / discipline groups / institutions
const numOfAchievableProgramsByDiscipline = (disciplineId: string) => {
  return achievablePrograms.value.filter(p => p.disciplines.some(d => d.id == disciplineId)).length;
}
const numOfAchievableProgramsByDisciplineGroup = (disciplineGroupId: string) => {
  return achievablePrograms.value.filter(p => p.disciplines.some(d => d.disciplineGroupId?.toString() == disciplineGroupId)).length;
}
const numOfAchievableProgramsByInstitution = (institutionId: string) => {
  return achievablePrograms.value.filter(p => p.institutionId == institutionId).length;
}

</script>

<style scoped>
  .featured-tag {
    color: #fff;
    background-color: var(--ion-color-danger);
  }

  .ad-banner {
    width: 100%;
    padding: 20px 0;
    color: #fff;
    text-align: center;
    font-size: 20px;
    background-color: transparent;
    margin: 4px 0;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
  }

  .predicted-score-filters {
    --background: var(--ion-color-primary);
  }
  .predicted-score-filters ion-segment-button {
    --indicator-color: var(--ion-color-fdmtred);
    --color-checked: #fff;
    --border-radius: 20px !important;
  } 

  .active-tag {
    background-color: var(--ion-color-fdmtred) !important;
  }

  /**
   * Discipline / Discipline Groups / Institution ion-chip
   */
  .material-div {
    padding: 4px !important;
  }
  .material-div ion-chip {
    height: 18px !important;
  }
</style>

<template>
  <!-- TODO: more spaces between & arrow positions -->

  <div class="program-content" style="width: 100%; display: flex; flex-direction: column; gap: 4px; padding: 0" :style="{ 'min-height': '20px' }">
    <!-- Program Deck Upper Card -->
    <div v-if="fromDeckView">
      <div>
        <div style="display: flex; align-items: center; flex-direction: row; flex-wrap: wrap; gap: 3px; justify-content: center"
              v-if="!hideBtns">
          <!-- Highlights Button -->
          <ion-button class="no-text-transform" size="small" color="primary" style="margin: 0; height: 24px; font-size: 0.75em" @click="openImageModal(item?.cardSlideImgLink)" v-if="item?.cardSlideImgLink">
            Highlights
          </ion-button>

          <!-- Institution Short Name + Program Website -->
          <ion-button class="no-text-transform" size="small" color="primary" style="margin: 0; height: 24px; font-size: 0.75em" @click.stop="item?.programWebsite ? openBrowser(item?.programWebsite) : undefined">
            {{ item?.institutionNameShort }}
          </ion-button>

          <!-- JUPAS Button -->
          <ion-button size="small" color="primary" style="margin: 0; height: 24px; font-size: 0.75em" @click.stop="item?.jupasUrl ? openBrowser(item?.jupasUrl) : undefined">
            {{ item?.jupasCode }}
          </ion-button>

          <!-- Strength2Careers -->
          <ion-button class="no-text-transform" size="small" color="primary" style="margin: 0; height: 24px; font-size: 0.75em"
                      @click="openAIProfessionDeckModal(item?.id)" v-if="allClientProgramIds.includes(item?.id)">
            Careers
          </ion-button>
        </div>
        <!--<h1 style="color: inherit; font-size: 0.9em; margin: 12px"><b>{{ item?.institutionNameShort }}</b></h1>-->
        <p v-if="!hideNames" style="color: #fff; font-size: 0.85em; margin: 8px 0 8px 0; text-align: center">{{ item?.name }}</p>
      </div>
    </div>

    <!-- Top Row: Title and Action Buttons -->
    <div v-else style="display: flex; gap: 8px; align-items: flex-start">
      <!-- Band Label & Score Icon-->
      <div style="display: flex; gap: 4px; align-items: center; flex-direction: column;">
        <b style="font-size: 24px" v-if="idx != null">{{ getBandLabel(idx) }}</b>
        <ion-icon size="large" :icon="ellipse" :color="targetScoreIconColor || 'danger'"></ion-icon>
        <ion-icon size="large" :icon="star" :color="schoolPredictScoreIconColor || 'danger'"></ion-icon>
        <!--<ion-icon :icon="star" color="danger"></ion-icon> warning? 1 more condition: next year -->
      </div>

      <!-- Left: Program Title -->
      <div style="flex: 1">
        <div>{{ item.jupasCode }}</div>
        <small style="margin: 0; font-size: 0.8em; line-height: 1.2">
          <span>{{ item.label }}</span>
        </small>
      </div>

      <!-- Right: Edit/Delete Buttons -->
      <!--<div style="display: flex; gap: 8px">
        <ion-button size="small" fill="clear" style="margin: 0; height: 24px; --padding-start: 0; --padding-end: 0"
                    @click="$emit('clickedPenBtn')">
          <ion-icon size="small" slot="icon-only" :icon="pencil"></ion-icon>
        </ion-button>
        <ion-button size="small" color="light" fill="clear" style="margin: 0; height: 24px; --padding-start: 0; --padding-end: 0"
                    @click.stop="$emit('clickedTrashBtn')" v-if="allowDelete">
          <ion-icon size="small" slot="icon-only" :icon="trashOutline"></ion-icon>
        </ion-button>
      </div>-->
    </div>

    <div :style="{ 'padding-left': fromDeckView ? '0' : '48px' }" v-if="showRecommendedEvents">
      <!-- Based on selected programs' discipline: 1 result only; show for students only -->
      <ion-item lines="full" button detail color="fdmtred">
        <ion-label class="ion-text-wrap">
          <p style="margin: 0; color: #fff">HK$300k per awardee scholarship</p>
        </ion-label>
      </ion-item>
    </div>

    <!-- Discipline Groups with Related Disciplines -->
    <div style="margin-top: 8px" v-if="!hideDisciplineBadges">
      <div class="badges-scroll-container" v-for="groupName in [...new Set(item.disciplines.map(d => d.groupName))]" :key="groupName">
        <!-- Group Name Badge -->
        <ion-badge :style="{ '--background': getDisciplineGroupColor(groupName), fontSize: '0.75em' }">
          {{ groupName }}
        </ion-badge>
        
        <!-- Related Disciplines in Scrollable Container -->
        <ion-badge v-for="discipline in item.disciplines.filter(d => d.groupName === groupName)" :key="discipline.id"
                  :style="{ '--background': getDisciplineGroupColor(discipline.groupName, true), fontSize: '10.4px', padding: '0 3px', height: '13px' }">
          {{ discipline.name }}
        </ion-badge>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// vue
import { computed, defineProps, defineEmits, } from 'vue';

// icon
import { checkmarkCircle, alertCircle, createOutline, pencil, trashOutline,
        handRightOutline, peopleOutline, personOutline, calendarClearOutline, calendarOutline,
        globeOutline, star, ellipse, close, thumbsUpOutline, } from 'ionicons/icons';

// components
import { IonItem, IonIcon, IonLabel, IonNote, IonSpinner, IonBadge, IonButton, IonFab, IonFabButton, } from '@ionic/vue';
import ABProfessionSelectModal from '@/components/pss/profession/ABProfessionSelectModal.vue';

// composables
import { utils } from '@/composables/utils';
import { useStore } from '@/store';

const { openModal, getBandLabel, getDisciplineGroupColor, openBrowser, openImageModal, } = utils();

const store = useStore();
const allClientProgramIds = computed(() => store.getters.allClientProgramIds);

const props = defineProps([
  "item", "idx", "fromDeckView", "allowDelete", "hideDisciplineBadges",
  "showRecommendedEvents", "hideNames", "hideBtns", "targetScoreIconColor", "schoolPredictScoreIconColor",
]);

const emits = defineEmits([
  "clickedPenBtn",
  "clickedTrashBtn"
]);

// For 'Careers' button
const openAIProfessionDeckModal = async (programId: any) => {
  const clientId = store.getters.getClientIdByProgramId(programId);
  return await openModal(ABProfessionSelectModal, { clientId, isFromProgramDeck: true });
}
</script>

<style scoped>
  p, a {
    color: #666666;
  }
  body.dark a {
    color: #666666 !important;
  }
  small a {
    color: var(--ion-color-step-600, #666666) !important;
  }

  ion-badge {
    margin-right: 3px;
  }
  .badges-scroll-container {
    display: flex;
    align-items: center;
    overflow-x: auto;
    gap: 4px;
    padding: 1px 0;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    -webkit-overflow-scrolling: touch;
  }
  .badges-scroll-container::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
  .badges-scroll-container ion-badge {
    flex-shrink: 0;
    margin-right: 0;
  }
</style>
<template>
  <ion-row class="ion-text-center subject-scores-row" v-if="showScorePlusOneSection" style="display: flex; align-items: center">
    <ion-col v-for="subject in userSubjects" :key="subject.shortName" class="subject-col" style="color: #fff; display: flex; align-items: center; justify-content: center; min-height: 50px"
            :style="{ 'background': plusOneSubjectId == subject.id ? 'var(--ion-color-primary)' : 'var(--ion-color-primary)' }">
      <div @click="togglePlusOneSubjectId(subject.id)" style="padding: 4px 0; width: 100%">
          <div class="grade">{{ plusOneSubjectId == subject.id ? (subjectScorePlusOneExtraPrograms[subject.id]?.length || 0) : '-' }}</div>
          <!--{{ subjectScorePlusOneExtraPrograms[subject.id]?.length >= 0 ? subjectScorePlusOneExtraPrograms[subject.id]?.length : '-' }}-->
          <div style="margin-top: -5px" v-if="showScorePlusOneSection && plusOneSubjectId == subject.id"><small>{{ getPlusOneSubjectGrade(plusOneSubjectId) }}</small></div>
      </div>
    </ion-col>
  </ion-row>
  
  <ion-row class="ion-text-center subject-scores-row" v-if="showSubjectNamesOnly">
    <ion-col v-for="subject in userSubjects" :key="subject.shortName" class="subject-col">
      <div class="score-box subject-name" style="justify-content: center" v-if="!hideSubjectNames" @click="checkOpenEditSubjectModal(subject)">
        {{ subject.shortName }}
      </div>
    </ion-col>
  </ion-row>

  <!-- Student target subject scores -->
  <div class="target-scores-section" :style="{
      'border-bottom-left-radius': noBottomBorderRadius ? '0' : '18px',
      'border-bottom-right-radius': noBottomBorderRadius ? '0' : '18px',
      'border-top-left-radius': noTopBorderRadius ? '0' : '18px',
      'border-top-right-radius': noTopBorderRadius ? '0' : '18px',
  }" :class="{ 'grade-select-large-font': gradeSelectLargeFont }" v-else>
    <!-- Overall Target Scores -->
    <ion-row class="ion-text-center subject-scores-row" v-if="!hidePredictedScoreInputs">
      <ion-col size="12" style="background: #2f2f2f; color: #fff">
        <div class="score-box" v-if="!passIcon" :class="{ 'small-title': smallTitles }">{{ title }} &nbsp;</div>
        <div class="score-box" v-else>
          {{ title }} &nbsp;
          <ion-icon color="success" :icon="passIcon" size="small"></ion-icon>Pass&nbsp;&nbsp;
          <ion-icon color="danger" :icon="passIcon" size="small"></ion-icon>Not Pass&nbsp;
        </div>
      </ion-col>

      <!-- Studying Subjects -->
      <ion-col v-for="subject in userSubjects" :key="subject.shortName" class="subject-col">
        <div class="score-box weighted-score grade" v-if="readonly" :class="{ 'blue-background': useBlueBackground }">
          {{ getSubjectGrade(subject.id) || '-' }}
        </div>
        <div class="score-box grade-select" @click="setPopoverOpen('grades', true, $event, subject.id)"
            :class="{ 'blue-background': useBlueBackground }" v-else>
          {{ getSubjectGrade(subject.id) || '-' }}
        </div>
        <div class="score-box subject-name" v-if="!hideSubjectNames" @click="checkOpenEditSubjectModal(subject)">
          {{ subject.shortName }}
        </div>

        <!-- Weighted score not working for 'Best of Subjects' -->
        <!--<div class="score-box weighted-score" v-if="fromDeckView">{{ getSubjectGrade(subject.id) ? getWeightedScore(program, subject.id, getSubjectGrade(subject.id)).weightedScore : '0' }}</div>-->
      </ion-col>
    </ion-row>

    <!-- Pass / not pass -->
    <ion-row style="font-size: 0.9em; color: #fff" v-if="fromDeckView && !hideProgramWeightedScores">
      <ion-col style="background: #656565; padding: 0"
                :style="{ 'background-color': `var(--ion-color-${getColorForScore()}` }">
        
        <ion-item lines="none" style="--background: transparent">
          <ion-label class="ion-text-center">
            <span style="font-size: 0.8em;">My weighted score: <b style="font-size: 1.3em;">{{ calculateBest5Scores() }}</b></span>

            <!-- Toggle Details -->
            <br />
            <ion-button size="small" @click.stop="toggleCalculationDetails" class="calculation-details-toggle-btn"
                        style="--padding-start: 8px; --padding-end: 8px; height: 16px; font-size: 0.75em; margin: 4px 0;">
              {{ calculationDetailsOpen ? 'Hide' : 'Breakdown' }}
            </ion-button>

            <div style="font-size: 0.8em">
              (<span v-if="notMeetProgramMinGradeRequirements">
                Not meet program entrance requirements
              </span>
              <span v-else-if="!passProgramElectiveRequirements">
                Not meet program elective requirements
              </span>
              <span v-else-if="!passUniEntranceRequirements">
                Not meet minimum university entrance requirements (332)
              </span>
              <span v-else-if="getAvailableScoreType(program) != 'N/A'">
                {{ getColorForScore() == 'fdmtred' ? 'Below' : 'Above' }} {{ getAvailableScoreType(program) }}
              </span>)
            </div>
          </ion-label>
        </ion-item>

        <!-- Calculation details content -->
        <div class="calculation-details" v-if="calculationDetailsOpen">
          <table class="calculation-table">
            <thead>
              <tr>
                <th>#</th>
                <th>Subject</th>
                <th>Min grade</th>
                <th>Your grade</th>
                <th>Score</th>
                <th>Weight</th>
                <th>Weighted score</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(step, index) in calculationSteps" :key="index"
                  :style="{ background: step.missingSubjects ? `var(--ion-color-danger)` : (step.scoreObj?.failedMinGrade ? `var(--ion-color-medium)` : undefined) }">
                <td>{{ index + 1 }}</td>
                <td>{{ step.missingSubjects ? step.missingSubjects.join('/') : (step.bonusPercent ? 'Bonus' : getSubjectName(step.scoreObj?.subjectId)) }}</td>
                <td>{{ step.scoreObj?.minGrade || '-' }}</td>
                <td>{{ step.scoreObj?.grade || '-' }}</td>
                <td>{{ step.scoreObj?.score || '-' }}</td>
                <td>{{ step.bonusPercent || step.scoreObj?.weighting || (step.multiplier ? step.multiplier : '1') }}</td>
                <td>{{ step.weightedScore || (step.scoreObj?.weightedScore ? (step.scoreObj?.weightedScore * (step.multiplier || 1)).toFixed(2) : '-') }}</td>
              </tr>
              <!-- Total row -->
              <tr class="total-row">
                <td colspan="4" class="total-label">Total</td>
                <td colspan="3">{{ calculateBest5Scores() }}</td>
              </tr>
            </tbody>
          </table>

          <!-- Elective Requirements Table -->
          <div v-if="Object.keys(groupedMinGradeSubjectRules).length > 0" class="elective-requirements">
            <table class="elective-table">
              <thead>
                <tr>
                  <th>Elective Group</th>
                  <th>Minimum Requirements</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(rules, groupName) in groupedMinGradeSubjectRules" :key="groupName">
                  <td class="elective-group">{{ groupName }}</td>
                  <td class="elective-options">
                    <span v-for="(rule, index) in rules" :key="index">
                      {{ getSubjectName(rule.subjectId) }} (Min: {{ rule.minGrade }})<span v-if="index < rules.length - 1"> or </span>
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

        </div>
      </ion-col>
    </ion-row>

    <!-- Popover for grade selection -->
    <ion-popover :isOpen="popoverState.grades" :event="popoverEvent" :dismiss-on-select="true" @didDismiss="setPopoverOpen('grades', false, $event)">
      <ion-content>
        <ion-item lines="full" v-for="grade in grades" :key="grade" @click="setTargetScore(grade)">
          <ion-label>{{ grade }}</ion-label>
        </ion-item>
        <ion-item lines="full" button @click="setTargetScore('')">
          <ion-label>-</ion-label>
        </ion-item>
      </ion-content>
    </ion-popover>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, defineProps, defineExpose, defineComponent, ref, reactive, computed, watch } from 'vue'

// icons
import { checkmark, close, ellipseOutline, triangleOutline, chevronDown, chevronUp, informationCircleOutline } from 'ionicons/icons';

// components
import { IonCol, IonIcon, IonRow, IonPopover, IonContent, IonItem, IonLabel, IonButton, } from '@ionic/vue';
import UserProfileFormModal from '@/components/modals/UserProfileFormModal.vue';

// composables
import { utils } from '@/composables/utils';
import { useStore } from '@/store';

// types
import { School } from '@/types';

// services
import AchieveJUPASService from '@/services/AchieveJUPASService';

const emit = defineEmits(['targetScoresChanged', 'plusOneSubjectIdChanged']);
const props = defineProps(["targetUser", "program", "fromDeckView", "title", "passIcon", "hideSubjectNames",
                            "readonly", "noBottomBorderRadius", "noTopBorderRadius", "gradeSelectLargeFont", "source",
                            "hidePredictedScoreInputs", "hideProgramWeightedScores", "showSubjectNamesOnly", "smallTitles",
                            "subjectScorePlusOneExtraPrograms", "showScorePlusOneSection", "useBlueBackground"]);
const { openModal, openServiceModal, getWeightedScore, getUserSubjects,
        calculateSumWeightedScores, getAvailableScoreType, getScoreDiff,
        isMeetUniEntranceRequirements, isMeetProgramElectiveRequirements,
        getFormattedGrade, getDSEScore, } = utils();

// Store
const store = useStore();
const user = computed(() => props.targetUser || store.state.user);
const userRelatedSchool = computed<School>(() => store.getters.userRelatedSchool);

// Constants (subjects & grades)
const allSubjects = computed(() => store.state.allSubjects);
const grades = ['5**', '5*', '5', '4', '3', '2', '1', 'U'];

// Get user's studying subjects with short names
const userSubjects = computed(() => {
  const userObj = props.targetUser || user.value;
  return getUserSubjects(allSubjects.value, userObj);
});

// Subject Grade (target / school prediction)
const getUserSubjectGrades = () => {
  const userObj = props.targetUser || user.value;
  return userObj.userSubjectGrades || [];
};
const getSubjectGrade = (subjectId: any) => {
  if (props.source == 'schoolPredict' && !userRelatedSchool.value.showPredictedScoresToStudents && props.readonly) {
    return '✱'; // not visible to students (requirements from school e.g. TWGHKYDS)
  }
  const res = getUserSubjectGrades().find(usg => usg.subjectId == subjectId && usg.source == props.source);
  return res?.grade;
};
const getPlusOneSubjectGrade = (subjectId: any) => {
  const res = getUserSubjectGrades().find(usg => usg.subjectId == subjectId && usg.source == 'overallTarget');
  if (!res?.grade) return '-';
  return getFormattedGrade(Math.min(7, getDSEScore({}, res?.grade)+1))?.toString();
}

// Target scores
const targetScores = reactive({
  chi: '',
  eng: '',
  math: '',
  csd: 'Attained',
});
const currPopoverSubjectId = ref('');
const setTargetScore = (grade) => {
  const subjectId = currPopoverSubjectId.value;
  const userSubjectGrades = getUserSubjectGrades();
  const updatedObj = userSubjectGrades.find(usg => usg.subjectId == subjectId && usg.source == props.source) ||
                    { subjectId, grade, source: props.source };
  updatedObj.userId = props.targetUser?.id || user.value.id; // may be for specific user
  updatedObj.grade = grade;
  updatedObj.updatedBy = user.value.id;
  AchieveJUPASService.upsertUserSubjectGrades([updatedObj]);

  // Update store props
  const targetUserObj = props.targetUser || user.value;
  const usg = targetUserObj.userSubjectGrades.find(usg => usg.subjectId == subjectId && usg.source == props.source);
  if (usg) usg.grade = grade;
  else {
    if (!Array.isArray(targetUserObj.userSubjectGrades)) targetUserObj.userSubjectGrades = [];
    targetUserObj?.userSubjectGrades.push(updatedObj);
  }
  //store.commit('upsertUserSubjectGrades', [updatedObj]); // Update user's own target scores
  emit('targetScoresChanged', targetScores);
};

// Popover
const popoverState = reactive({
  seats: false,
  grades: false,
});
const popoverEvent = ref();
const setPopoverOpen = (popoverKey: any, state: boolean, ev: any, currSelectedSubjectId?: any) => {
  popoverEvent.value = ev;
  popoverState[popoverKey] = state;
  currPopoverSubjectId.value = currSelectedSubjectId
};

// Calculation details accordion
const calculationDetailsOpen = ref(false);
const toggleCalculationDetails = () => {
  calculationDetailsOpen.value = !calculationDetailsOpen.value;
};
const calculationResults = computed(() => calculateSumWeightedScores(allSubjects.value, userSubjects.value, getUserSubjectGrades(), props.program, props.source));
const calculationSteps = computed(() => calculationResults.value.calculationSteps.slice());
const notMeetProgramMinGradeRequirements = computed(() => calculationResults.value.notMeetMinGradeSubjects?.length > 0);
const passUniEntranceRequirements = computed(() => isMeetUniEntranceRequirements(getUserSubjectGrades().filter(sg => sg.source == props.source)));
const passProgramElectiveRequirements = computed(() => isMeetProgramElectiveRequirements(props.program, allSubjects.value, userSubjects.value, getUserSubjectGrades(), props.source));
const groupedMinGradeSubjectRules = computed(() => calculationResults.value.groupedMinGradeSubjectRules); // min grade requirements for elective 1 & 2 (failed to achieve)

// Get subject name from ID
const getSubjectName = (subjectId: number) => {
  const subject = allSubjects.value.find(s => s.id === subjectId);
  return subject ? subject.shortName : 'Unknown';
};

// Display weighted score for any subject (default latest year)
const calculateBest5Scores = (targetRuleYear: any = null) => {
  const result = calculateSumWeightedScores(allSubjects.value, userSubjects.value, getUserSubjectGrades(), props.program, props.source, targetRuleYear);
  return result.totalScores.toFixed(1);
};

const getColorForScore = () => {
  if (notMeetProgramMinGradeRequirements.value) return 'fdmtred';
  if (!passProgramElectiveRequirements.value) return 'medium';
  if (!passUniEntranceRequirements.value) return 'fdmtred';
  const scoreDiff = getScoreDiff(props.program, allSubjects.value, userSubjects.value, getUserSubjectGrades(), props.source);
  if (scoreDiff == null) return 'light';
  if (scoreDiff >= 0) return 'darkgreen';
  //else if (scoreDiff >= -2) return 'warning'; // TBC (PTCC VPr. Chan prefers this)
  return 'fdmtred';
};

// Edit Subject (Electives)
const checkOpenEditSubjectModal = async (subject) => {
  if (!props.targetUser && !["Chinese Language", "English Language", "Mathematics"].includes(subject.name)) {
    await openModal(UserProfileFormModal, { fromAchieveJUPAS: true });
  }
}

// Track clicked subjects (emit events)
//const plusOneSubjectId = ref<any>(null); // when subject score + 1
const plusOneSubjectId = ref<any>(24); // Default select Chinese
const togglePlusOneSubjectId = (newSubjectId) => {
  if (props.subjectScorePlusOneExtraPrograms[newSubjectId]?.length >= 0) {
    //plusOneSubjectId.value = (newSubjectId == plusOneSubjectId.value ? null : newSubjectId);
    plusOneSubjectId.value = newSubjectId;
    emit('plusOneSubjectIdChanged', plusOneSubjectId.value);
  }
}
</script>

<style scoped>
/* Elective Requirements Table */
.elective-requirements {
  margin: 16px 0;
  font-size: 0.9em;
}

.elective-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 8px;
  background: #fff;
  border: 1px solid #e0e0e0;
}

.elective-table th,
.elective-table td {
  padding: 10px 12px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
  color: #2c3e50;
}

.elective-table th {
  background-color: #f5f5f5;
  font-weight: 600;
  color: #2c3e50;
}

.elective-table tr:last-child td {
  border-bottom: none;
}

.elective-group {
  font-weight: 500;
  white-space: nowrap;
  padding-right: 24px;
}

.elective-options {
  color: #2c3e50;
  line-height: 1.4;
}

/**
 * Target Scores Section
 */
 .target-scores-section {
  border-radius: 18px;
  overflow: hidden;
  margin: 0;
}
.subject-scores-row {
  border-radius: 12px;
}
.subject-scores-row ion-col {
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
}
.score-box {
  font-size: 0.65em;
  text-align: center;
  min-height: 20px;
  display: flex;
  align-items: center;
  justify-content: left;
  padding-left: 16px;
}
.weighted-score {
  background: #656565;
  color: #fff;
}
.subject-name {
  padding: 4px;
  background: #2f2f2f;
  color: #fff;
}
.blue-background {
  background: var(--ion-color-primary) !important;
}
.grade-select {
  color: #fff;
  background: #656565;
  min-height: 28px !important;
  /*font-size: 0.85em;*/
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  font-size: 24px !important;
  font-weight: bold;
}
.grade-select::after {
  content: '▼';
  /*font-size: 0.7em;*/
  font-size: 12px !important;
  color: var(--ion-color-medium);
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}
.grade-select:hover {
  background-color: var(--ion-color-light-shade);
}
.grade-select:active {
  background-color: var(--ion-color-light-tint);
}
.grade {
  font-weight: bold;
  font-size: 24px !important;
}

.grade-select-large-font .grade {
  font-size: 0.85em !important;
  font-weight: normal;
}

/* Calculation details accordion */
.calculation-details-toggle-btn {
  text-transform: none;
  --background: rgba(255, 255, 255, 0.1);
  transition: background-color 0.2s ease;
}
.calculation-details-toggle {
  margin-top: 8px;
  padding: 4px 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.calculation-details-toggle:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.calculation-details-toggle ion-icon {
  margin-right: 4px;
  font-size: 16px;
}

.calculation-details {
  margin-top: 8px;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow-x: auto;
}

.calculation-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.8em;
}

.calculation-table th,
.calculation-table td {
  font-weight: normal !important;
  font-size: 0.7em !important;
  padding: 4px 8px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.calculation-table th {
  background-color: rgba(0, 0, 0, 0.2);
  font-weight: bold;
}

.calculation-table .total-row {
  background-color: rgba(0, 0, 0, 0.15);
  font-weight: bold;
}

.calculation-table .total-label {
  text-align: right;
  padding-right: 16px;
}

.small-title {
  font-size: 8px !important;
  justify-content: center;
  min-height: 10px !important;
  background: var(--ion-color-light) !important;
}
</style>

/**
 * UST special handling for 6th subject bonus
 * Ref: https://publication.join.ust.hk/view/13965330/18-19/#zoom=true
 */
const getUST6thSubjectBonus = (score) => {
  if (score >= 8.5) return 0.05; // 5%
  if (score >= 7) return 0.0412; // 4.12%
  if (score >= 5.5) return 0.0324; // 3.24%
  if (score >= 4) return 0.0235; // 2.35%
  if (score >= 3) return 0.0176; // 1.76%
  return 0;
}

/**
 * AchieveJUPAS
 */
const getDSEScore = (program: any, grade, targetRuleYear: any = null) => {
  const { scoreRules } = program;
  const ruleObj = scoreRules ? (scoreRules.find(sr => sr.intakeYear == targetRuleYear) || scoreRules[0]) : {};
  switch(grade) {
    case '5**': return ruleObj?.level5ss || 7;
    case '5*': return ruleObj?.level5s || 6;
    case '5': return ruleObj?.level5 || 5;
    case '4': return ruleObj?.level4 || 4;
    case '3': return ruleObj?.level3 || 3;
    case '2': return ruleObj?.level2 || 2;
    case '1': return ruleObj?.level1 || 1;
    default: return ruleObj?.levelU || 0;
  }
}
const getFormattedGrade = (grade: any) => {
  if (grade == 'U') return 'U';
  if (grade == 1) return 1;
  if (grade == 2) return 2;
  if (grade == 3) return 3;
  if (grade == 4) return 4;
  if (grade == 5) return 5;
  if (grade == 6) return '5*';
  if (grade == 7) return '5**';
  if (grade == '5*') return '5*';
  if (grade == '5**') return '5**';
  return null; // Not avaiable
}
const getWeightedScore = (program, subjectId, grade, targetRuleYear: any = null) => {
  subjectId = Number(subjectId);
  const score = getDSEScore(program, grade, targetRuleYear);
  const { subjectRules } = program;
  const relatedSubjectRules = subjectRules?.filter(sr => sr.subjectId == subjectId) || [];
  const { weighting, minGrade, } = relatedSubjectRules?.find(sr => sr.intakeYear == targetRuleYear) || relatedSubjectRules[0] || { weighting: 1 }; // default weighting: 1
  return { score, weightedScore: score * weighting, weighting, minGrade, };
}

// Minimum University Entrance Requirements
// Ref: https://www.hkeaa.edu.hk/en/recognition/local_studies_and_employment/local/
const isMeetUniEntranceRequirements = (relatedSubjectGrades: any) => {
  const minRequirements = [
    { subjectId: '24', minScore: 3 }, // Chinese
    { subjectId: '25', minScore: 3 }, // English
    { subjectId: '26', minScore: 2 }, // Maths
  ];
  return minRequirements.every(r => relatedSubjectGrades.find(sg => sg.subjectId == r.subjectId && getDSEScore({}, sg.grade) >= r.minScore));
}

const getUserSubjects = (allSubjects, userObj) => {
  const coreSubjects = ["Chinese Language", "English Language", "Mathematics"];
  const electives = Array.isArray(userObj.studyingElectives) ? userObj.studyingElectives
                        : (userObj.studyingElectives || "").toString().split(" , ").filter(e => !!e);

  if (!userObj.isSecondaryStudent && electives.length == 0) {
    return [...coreSubjects, "Physics", "Chemistry", "Mathematics Extended Part Module 1 (M1)"]
            .map(e => allSubjects.find(se => se.name === e));
  }
  return [...coreSubjects, ...new Set(electives)]
          .map(e => allSubjects.find(se => se.name === e))
          .filter(e => !!e);
}
const getSubjectGrade = (userSubjectGrades, subjectId: any, source: any) => {
  const res = userSubjectGrades.find(usg => usg.subjectId == subjectId && usg.source == source);
  return res?.grade;
};

const calculateSumWeightedScores = (allSubjects, userSubjects, userSubjectGrades, program, source, targetRuleYear: any = null) => {
  // 0. Convert subject grade to grade points based on program_subject_grade & get weighted scores for each subject
  const subjectScores = userSubjects.map(s => {
    const grade = getSubjectGrade(userSubjectGrades, s?.id, source);
    const weightedScoreObj = getWeightedScore(program, s?.id, grade, targetRuleYear);
    return {
      subjectName: s?.shortName,
      subjectId: s?.id, grade,
      ...weightedScoreObj,
      failedMinGrade: weightedScoreObj.minGrade ? getDSEScore({}, grade) < getDSEScore({}, weightedScoreObj.minGrade) : false,
    }
  });

  // 1. Sort scores from highest to lowest (for check & apply correct rules in min_grade_group)
  let sortedScores = subjectScores.slice().sort((a, b) => b.score - a.score);

  // 1.5. Apply correct subject rule (based on min_grade_group)
  const usedElectiveGroups = {}; // Mainly track Elective 1 & Elective 2
  for (const scoreObj of sortedScores) {
    let relatedSubjectRules = program.subjectRules?.filter(sr => sr.subjectId == scoreObj.subjectId && sr.intakeYear == targetRuleYear) || [];
    if (relatedSubjectRules.length == 0) relatedSubjectRules = program.subjectRules?.filter(sr => sr.subjectId == scoreObj.subjectId) || [];
    const targetRule = relatedSubjectRules?.find(sr => !Object.keys(usedElectiveGroups).includes(sr.minGradeGroup)) || { weighting: 1 };
    if (targetRule.minGradeGroup) {
      usedElectiveGroups[targetRule.minGradeGroup] = true;
    }
    scoreObj.weighting = targetRule.weighting; // update weighting
    scoreObj.weightedScore = scoreObj.score * targetRule.weighting; // update weighted score
  }

  // 2. Sort weighted scores from highest to lowest
  sortedScores = sortedScores.slice().sort((a, b) => b.weightedScore - a.weightedScore);

  // 2.5 Best of subjects weighting handling
  const allBosSubjectRules = program.subjectRules?.filter(sr => sr.bestOfSubjects && sr.weighting) || [];
  if (allBosSubjectRules.length > 0) {
    let filteredSubjectRules = allBosSubjectRules.filter(sr => sr.intakeYear == (targetRuleYear || new Date().getFullYear()));
    if (filteredSubjectRules.length == 0) filteredSubjectRules = allBosSubjectRules;
    for (const rule of filteredSubjectRules) {
      const { bestOfSubjects, weighting } = rule;
      const subjectNames = bestOfSubjects.split('/').map(s => s.trim());
      const subjectIds = subjectNames.map(sn => allSubjects.find(s => s.name == sn || s.shortName == sn)?.id);
      for (const scoreObj of sortedScores) {
        if (subjectIds.some(id => id == scoreObj.subjectId)) {
          scoreObj.weightedScore = scoreObj.score * weighting;
          scoreObj.weighting = weighting;
          break; // Done: found best of subjects & added weighting
        }
      }
    }
  }

  // 3. Loop from subject1 to subject7, filter out N/A ones & sum the weighted scores (or times 0.X for 6th/7th electives)
  const { scoreRules, institutionId, } = program; // Get rule of specific year (for correct comparison with past admission score)
  const ruleObj = scoreRules ? (scoreRules.find(sr => sr.intakeYear == (targetRuleYear || new Date().getFullYear())) || scoreRules[0]) : {};
  const calculationSteps: any = [], missingSubjects: any = [];

  let totalScores = 0;
  for (const k of Object.keys(ruleObj).filter(k => k.startsWith('subject')).sort()) {
    const rule = ruleObj[k];

    // Special handling for UST 6th elective bonus
    // Ref: https://publication.join.ust.hk/view/13965330/18-19/#zoom=true
    if (k == 'subject6' && institutionId == 6) {
      const nextScore = sortedScores.shift();
      if (nextScore) {
        // 6th subject grade -> bonus %
        const bonusPercent = getUST6thSubjectBonus(nextScore.score);

        // Consider all current subjects, assume all 5**
        const highestAttainableScores = userSubjects.map(s => {
          const grade = '5**';
          return {
            subjectId: s?.id, grade,
            ...getWeightedScore(program, s?.id, grade, targetRuleYear),
          }
        });

        // Sum the best 5 weighted scores
        const best5Scores = highestAttainableScores.sort((a, b) => b.weightedScore - a.weightedScore)
                                                    .slice(0, 5).reduce((sum, s) => sum + s.weightedScore, 0);

        // Bonus = best5Scores * bonus %
        totalScores += best5Scores * bonusPercent;
        calculationSteps.push({ bonusPercent: `${(bonusPercent*100).toFixed(2)}%`, scoreObj: { score: best5Scores }, weightedScore: (best5Scores*bonusPercent).toFixed(3) });
      }
    }
    else {
      if (!rule || rule == 'N/A') continue;
      if (rule == 'Any') { // can be any subjects
        const nextScore = sortedScores.shift();
        if (nextScore) {
          totalScores += nextScore.weightedScore;
          calculationSteps.push({ scoreObj: nextScore, weightedScore: nextScore.weightedScore });
        } else {
          missingSubjects.push(rule);
        }
        continue;
      }
      if (!isNaN(Number(rule))) { // mainly for HKU 7th elective bonus (x0.2)
        const nextScore = sortedScores.shift();
        if (nextScore) {
          totalScores += (nextScore.weightedScore || 0) * Number(rule);
          calculationSteps.push({ scoreObj: nextScore, multiplier: Number(rule) });
        }
        continue;
      }

      // Must be specific subjects
      const acceptedSubjects = rule.split(',').map(s => s.trim());
      let foundSubject = false;
      for (let i = 0; i < sortedScores.length; i++) {
        const score = sortedScores[i];
        const subjectName = allSubjects.find(s => s.id == score.subjectId)?.shortName;
        if (acceptedSubjects.includes(subjectName)) {
          totalScores += score.weightedScore;
          calculationSteps.push({ scoreObj: score, mustInclude: true, });
          foundSubject = true;
          sortedScores.splice(i, 1); // remove from scores to prevent duplicate calculation
          break;
        }
      }
      if (!foundSubject) {
        missingSubjects.push(acceptedSubjects);
        calculationSteps.push({ missingSubjects: acceptedSubjects, multiplier: Number(rule) });
      }
    }
  }

  const notMeetMinGradeSubjects = calculationSteps.filter(s => s.scoreObj?.failedMinGrade).map(s => s.scoreObj?.subjectName);

  // Check program_subject_rules -> min_grade_group + min_grade
  // for handling exceptional cases like JS1202 (https://www.jupas.edu.hk/en/programme/cityuhk/JS1202 - Best 4)
  const relatedSubjectRules = program.subjectRules?.filter(sr => sr.minGrade && sr.minGradeGroup) || [];
  let filteredSubjectRules = relatedSubjectRules.filter(sr => sr.intakeYear == (targetRuleYear || new Date().getFullYear()));
  if (filteredSubjectRules.length == 0) filteredSubjectRules = relatedSubjectRules;

  // Group by minGradeGroup (pass minimum elective requirements)
  const groupedMinGradeSubjectRules = filteredSubjectRules.reduce((acc, sr) => {
    if (!acc[sr.minGradeGroup]) acc[sr.minGradeGroup] = [];
    acc[sr.minGradeGroup].push(sr);
    return acc;
  }, {});
  for (const group in groupedMinGradeSubjectRules) {
    const rules = groupedMinGradeSubjectRules[group]; // Need to pass any 1 rule in the group
    for (const rule of rules) {
      const usc = subjectScores.find(s => s.subjectId == rule.subjectId);
      if (usc?.grade >= rule.minGrade) {
        delete groupedMinGradeSubjectRules[group]; // passed
        break;
      }
    }
  }

  return { totalScores, calculationSteps, missingSubjects, notMeetMinGradeSubjects, groupedMinGradeSubjectRules, };
}
const getAvailableScoreType = (program) => {
  const { admissions, } = program;
  const { scoreLowerQuartile, scoreMedian, scoreMean } = admissions?.[0] || {};
  if (scoreLowerQuartile) return 'LQ';
  if (scoreMedian) return 'Median';
  if (scoreMean) return 'Mean';
  return 'N/A';
}

// Score difference between admission score & predicted score (weighted)
// calculate score using same rule (past score's intake year)
const getScoreDiff = (program, allSubjects, userSubjects, userSubjectGrades, source) => {
  const { admissions, } = program;
  const { scoreLowerQuartile, scoreMedian, scoreMean, intakeYear } = admissions?.[0] || {};
  const { totalScores: score } = calculateSumWeightedScores(allSubjects, userSubjects, userSubjectGrades, program, source, intakeYear);
  const compareScore = scoreLowerQuartile || scoreMedian || scoreMean;
  if (!compareScore) return null;
  return score - compareScore;
}

const isMeetProgramElectiveRequirements = (program, allSubjects, userSubjects, userSubjectGrades: any = [], source: any = null) => {
  const { missingSubjects, groupedMinGradeSubjectRules, } = calculateSumWeightedScores(allSubjects, userSubjects, userSubjectGrades, program, source);
  return Object.keys(groupedMinGradeSubjectRules).length == 0 && missingSubjects.length == 0;
}

const isAchievableProgram = (program, allSubjects, userSubjects, userSubjectGrades: any = [], source: any = null) => {
  const passUniEntranceRequirements = isMeetUniEntranceRequirements(userSubjectGrades.filter(sg => sg.source == source));
  const passProgramElectiveRequirements = isMeetProgramElectiveRequirements(program, allSubjects, userSubjects, userSubjectGrades, source);
  const { notMeetMinGradeSubjects, } = calculateSumWeightedScores(allSubjects, userSubjects, userSubjectGrades, program, source);

  if (notMeetMinGradeSubjects.length > 0 || !passProgramElectiveRequirements || !passUniEntranceRequirements) return false;
  const scoreDiff = getScoreDiff(program, allSubjects, userSubjects, userSubjectGrades, source);
  if (scoreDiff == null) return false;
  if (scoreDiff >= 0) return true;
  return false;
}
  
export default {
  getDSEScore, getFormattedGrade,
  getWeightedScore,
  getUserSubjects,
  calculateSumWeightedScores,
  getAvailableScoreType,
  getScoreDiff,
  isMeetUniEntranceRequirements,
  isMeetProgramElectiveRequirements,
  isAchievableProgram,
}
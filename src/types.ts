// Advertisement (AchieveJUPAS tag -> banner)
export interface Advertisement {
  id: any,
  clientId?: any,
  disciplineId?: any,
  disciplineGroupId?: any,
  bannerImgLink?: string,
  link?: string,
}

// AchieveJUPAS
export interface UserSubjectGrade {
  userId?: string;
  subjectId: any;
  source?: string;
  grade?: string;
  createdAt?: any;
  updatedAt?: any;
  createdBy?: any;
  updatedBy?: any;
}
export interface Tag {
  id: string;
  name: string;
  type?: string;
  createdBy: string;
  createdAt?: string;
}

// SLP
export interface DisciplineClaim {
  id: string;
  disciplineId: any;
  clientId: any;
  text: string;
  elaboration: string;
  order: number;
  userText?: string;
  userElaboration?: string;
  userOrder?: number;
  selected?: boolean;
}
export interface UserClaim {
  //id: string;
  userId?: string;
  claimId: string;
  text: string;
  elaboration: string;
  selected: boolean;
  order: number;
  type: string;
  createdAt?: string;
  updatedAt?: string;
}
export interface UserSLP {
  userId: string;
  original: string;
  gpt: string;
  accessType: string;
}

// CL
export interface UserCLClaim {
  id?: string;
  text?: string;
  expIds?: any[];
  expElaboration?: string;
  createdAt?: string;
  placeholder?: string; // used in App only
}
export interface UserCLExp {
  id?: string;
  text?: string;
  createdAt?: string;
  placeholder?: string; // used in App only
}

// Anchor Event Tasks & Form Questions
export interface Lesson {
  id: string;
  sessionId: string;
  campaignGroup: string;
  serviceId: string;
  lessonDisplayName: string;
  lessonShortName: string;
  lessonDescription: string;
  defaultMode: string;
  defaultStartTime: string;
  duration: string;
}
export interface AnchorEventTask {
  id: string;
  anchorEventId: string;
  title: string;
  description: string;
  group: string;
  showAfterAttended: boolean;
  formQuestions: any[];
}
export interface FormQuestion {
  id: string;
  title: string;
  type: string;
  options: string[];
  lsLowerScaleLabel: string;
  lsUpperScaleLabel: string;
  isRequired: boolean;
}
export interface FormResponse {
  userId: string;
  sessionId: string;
  taskId: string;
  questionId: string;
  questionTitle: string;
  answer: string;
}

// ABS (Schools / Intakes / Intake Sessions / Step 1 Questions)
export interface School {
  id: any;
  name: string;
  nameChi: string;
  nameShort: string;
  logoLink: string;
  bannerLink: string;
  step1Enabled: boolean;
  absIntakeId: string;
  f3ClassChart?: string;
  remarks?: string;
  achievejupasStatus?: string;
  achievejupasNeedParentConsent?: boolean;
  achievejupasDeadline1?: string;
  achievejupasDeadline2?: string;
  achievejupasT1Briefing?: string;
  achievejupasT2Briefing?: string;

  showPredictedScoresToStudents?: boolean;
  band: string;
  district: string;
  language: string;
  type: string;

  numOfF4Downloads?: number;
  numOfF5Downloads?: number;
  numOfF6Downloads?: number;
  numOfRegisteredTeachers?: number;

  // App only
  userConsentRecords?: UserConsentRecord[];
}
export interface ABSIntake {
  id: any;
  schoolId: string;
  academicYear: string;
  name: string;
  sessions: Session[];
}
export interface Step1Option {
  id: any;
  text: string;
  tabIds: any[];
  professionIds: any[];
  questionId: string;
  disciplineIds: any[];
  cardSpecs: CardSpec[];
}
export interface Step1Question {
  id: any;
  order: any;
  title: string;
  group: string;
  options: Step1Option[];
  schoolId: string;
  programId: string;
}
export interface Card {
  id: any;
  title: string;
  imageLink: string;
  description: string;
  type: string;
  btn1Text: string;
  btn1Link: string;
  btn2Text: string;
  btn2Link: string;
  btn3Text: string;
  btn3Link: string;
}
export interface CardSpec {
  cardId: any;
  order: any;
  position: any;
}


// Work (Seminars & Visits)
export interface Service {
  id: any;
  type: string;
  nature: string;
  name: string;
  tagline: string; // Ace university interview with ESG
  currIntakeYear: string;
  description: string;

  posterLink: string;
  extraPosterLinks: any[];
  slideLink: string;

  videoLink: string;
  embeddedYTLink: string;
  embeddedVideoSlideLink: string;
  pastStudentSharingVideoIds: any[];

  status?: string;
  isAllowProposeDates: boolean;
  targetSchoolBands: any[];
  targetForms: any[];
  isForPrincipalOnly: boolean;

  isPopoverOpened?: any;
  popoverEvent?: any;

  lastAppliedSession?: Session;

  university?: string;
  clientId?: string;
  anchorEventId?: string;

  eventAlbumLink?: string; // Album link
  eventPhotoLinks?: any[]; // Service Photos (e.g. BBAWork workshop photos etc.)
  shootingPhotoLinks?: any[]; // Past shooting moments
  clientPhotos?: any[]; // Demo certificates (by client)
}

export interface Session {
  group: string; // Work / ABS / jobEX
  subgroup: string; // SLP / Workshop
  relatedProgramId: string;
  clientSchoolId: string;
  inChargeTeacherNames: string; // mainly the teacher who proposed the session

  absIntakeId: string;
  anchorEventId: string;
  anchorEventName: string;
  studentForms: string;
  numOfStudents: number;
  classes: string;

  id: any;
  status: string; // TBC / Confirmed
  name: string;
  displayName: string; // include mode as well
  formattedName: string;
  description: string;
  remarks: string;
  language: string;
  department: string;
  clientId: string;
  mode: string;
  venue: string;
  type: string; // Seminar / Visit
  startTimeStr: string;
  endTimeStr: string;
  date: any;
  formattedDateTime: string;
  onlineMeetingLink: string;
  onlineMeetingPsw: string;
  videoLink: string;
  serviceId: string;
  serviceName: string;
  serviceTagline: string;
  serviceType: string;
  serviceNature: string;
  serviceUniversity: string;
  serviceTargetForms: any[];
  serviceTargetBands: any[];
  rollCallCode: string;
  userResponse?: EventResponse;
  targetDisciplineGroups: any[];
  targetDSEYears: any[];
  posterLink: string;

  // jobEX
  jobEXIntakeId: string;
  remainingSeats: any; // for showing vacancy of sessions
  quota?: any;

  // Special
  schoolAddrGmapEmbedLink: string;

  startTime: any;
  arrivalTime: any;
  roomAvailableTime: any;
  endTime: any;
  duration: any;

  // Interview
  needInterview?: boolean;

  // Video Shooting
  needParentConsent?: boolean;
}

export interface EventResponse {
  id?: any;
  sessionId: string;
  userId: string;
  response: string;
  attended?: string;
  confirmed: string;
  rollCallCode?: string;
  rollCallTime?: string;
  nominatedBy: string;
  lessonId?: string;
  createdAt?: string;
  updatedAt?: string;

  interviewStatus?: string;
  interviewRank?: any;
  workshopTimePerference?: string;
  assignedWorkshopSession?: string;
  interviewerComments?: string;
  isOutstandingStudent?: boolean;
}

export interface ServiceResponse {
  id?: any;
  serviceId: string;
  userId: string;
  response: string;
  createdAt?: string;
  updatedAt?: string;
}

// Secondary School Student
export interface SecondarySchoolStudent {
  userId?: any;
  group?: string;
  q1Ans?: string;
  q2Ans?: string;
  q3Ans?: string;
  referrerPhone?: string;
  isPrefect?: boolean;
  referrerRole?: string;
  referrerName?: string;
  howToKnow?: string;
}
// University Student
export interface UniversityStudent {
  userId?: any;
}
// University Client / Staff
export interface UniversityClient {
  userId?: any;
  employmentStatus?: string;
  clientId?: string;
  institution?: string;
  position?: string;
  remarks?: string;
}
// Secondary School Teacher
export interface Teacher {
  userId?: any;

  employmentStatus?: string;
  schoolRoles?: string;
  remarks?: string
  advisorCandidate?: string;
  source?: string;

  classRoles?: TeacherClassRole[];
}
export interface TeacherResponseAnswer {
  teacherResponseId: string;
  questionId: string;
  questionTitle: string;
  answer: string;
  serviceId?: string;
  eventName?: string;
  optionId?: string;
}
export interface TeacherResponse {
  id?: any;
  eventName?: string;
  serviceId?: string;
  sessionId?: string;
  response?: string;
  estNumOfStudents?: string;
  preferredSessionDates?: string;
  preferredSessionTime?: string;
  preferredVenue?: string; // main for submitting AB4 session info
  optionId?: string;
  createdAt?: string;
  updatedAt?: string;
  type?: string;
  questionAnswers?: TeacherResponseAnswer[];
  role?: string;
  intakeYear?: string;
  targetClass?: string; // class-based response tracking
  targetForm?: string; // form-based response tracking
}
export interface TeacherResponseOption {
  id: any;
  title?: string;
  note?: string;
  order?: number;
  showNominateBtn?: boolean;
  showIfExistSessions?: boolean;
  actionCreate?: any;
  immediateAction?: any; // e.g. on select option open modal, etc.
  targetRole?: any; // role to be changed to after selecting the option
}
export interface TeacherResponseQuestion {
  id: any;
  serviceId?: any;
  anchorEventId?: any;
  group?: any;
  order?: any;
  title: string;
  subtitle?: string;
  type?: string;
  isRequired?: boolean;
  showInOptionIds?: string;
  options: TeacherResponseOption[];
  relatedTeacherResponseField?: string;
}
export interface TeacherClassRole {
  id: any;
  userId: any;
  role: string; // Class teacher / ICT teacher / ...
  classes: string;
  remarks?: string;
  status?: string;
  subject?: string; // Chinese / English / ...
  createdAt: string;
  updatedAt: string;
}

// User
export interface User {
  row?: string;
  id?: any;
  loggedInWithDemoLink?: boolean;

  fullName?: string;
  preferredName?: string;
  chineseName?: string;
  email?: string;
  phone?: string;
  gender?: string;
  isPhoneVerified?: boolean;

  schoolId?: string;
  class?: string;
  studentNumber?: string;
  studyingCurriculum?: string;
  studyingElectives?: string;
  yearDSE?: any;
  rankInForm?: string;

  preferredLang?: string; // en or zh
  profilePic?: string;
  accessDisabled?: boolean; // Y or N
  darkTheme?: boolean; // Y or N
  isAdmin?: boolean;
  isCamXApplicant?: boolean; // CambridgeX applicants
  isSecondaryStudent?: boolean; // Secondary School Students
  isUniversityStudent?: boolean; // University Students (jobEX)
  isUniversityClient?: boolean; // Clients / Professors (University staff)
  isEmployer?: boolean;
  roles?: string;
  firstSelectedDisciplineNames?: string;
  step1OptionIds?: string;
  step1OrderedProfessionIds?: string;
  step1ProfessionReason?: string;
  step1ProfessionAction?: string;
  step2DisciplineIds?: string;
  step2DisciplineReason?: string;
  step2DisciplineAction?: string;
  step3ProgramIds?: string;
  lastSelectedProfessions?: Profession[];
  lastSelectedDisciplines?: Discipline[];
  lastSelectedPrograms?: Program[];
  lastSelectedStep1Options?: Step1Option[];

  likedProfessionIds?: any[];
  browsedProfessionIds?: any[];
  likedSectorIds?: any[];
  browsedSectorIds?: any[];
  likedSegmentIds?: any[];
  browsedSegmentIds?: any[];

  absIntakeId?: string;
  heardOfProfessionIds?: any[];
  notHeardOfProfessionIds?: any[];

  verificationResult?: any;
  nominatedBy?: any;

  sessionResponse?: EventResponse; // mainly for professor's view
  sessionResponses?: EventResponse[];
  formResponses?: FormResponse[];
  serviceResponses?: ServiceResponse[];

  // WhatsApp
  waGroupId?: string;
  waGroupLink?: string;
  waPhoneNumber?: string;
  userInWaGroup?: boolean;

  // Child Tables
  teacher?: Teacher;
  universityStudent?: UniversityStudent;
  secondarySchoolStudent?: SecondarySchoolStudent;
  universityClient?: UniversityClient;

  // University Client
  clientId?: any;

  // For Nomination
  isNewStudent?: boolean;
  timePreference?: string;

  // For demo
  appRole?: any;

  // SLP
  claims?: UserClaim[];
  slp?: UserSLP;

  // jobEX
  jobEX?: string;
  programId?: string;
  isCvReady?: boolean;
  isAmbassador?: boolean;
  uniEmail?: string;
  nationality?: string;
  studentId?: string;
  profileDoc?: string;
  currIntakeId?: string;
  yearOfStudy?: string;
  createdAt?: string;

  l1?: string;
  l2?: string;
  l3?: string;

  // New (discipline / profession reactions)
  userDisciplines?: UserDiscipline[];
  userProfessions?: UserProfession[];
  userElectives?: UserElective[];
  userSegments?: UserSegment[];
  userPrograms?: UserProgram[];
  userEmployerJobs?: UserEmployerJob[];
  userUsers?: UserUser[]; // mainly for commenting on other users
  votedByUsers?: UserUser[]; // mainly for workshop best presenters (know who vote the user)
  numOfVotes?: any;
  rank?: number;

  // CL
  clClaims?: UserCLClaim[];
  clExps?: UserCLExp[];

  // Shared between clients & teachers
  teacherResponses?: TeacherResponse[];

  // For Filtering (AchieveJUPAS)
  tagIds?: any[];
  createdTags?: Tag[];
  filteredSessionResponses?: EventResponse[];

  // AchieveJUPAS
  userSubjectGrades?: UserSubjectGrade[];
  isDemo?: boolean; // App only
  likedProgramCount?: number; // App only

  // MOU / User Consent
  userConsentRecords?: UserConsentRecord[];
  relatedVideos?: any[];

  // Mega Student List
  selectedClientProgram?: boolean;
}
export interface UserConsentRecord {
  id: string;
  createdAt?: any;
  target: string;
  signatureImg?: string;
  response: string;
  roles: any;
  schoolId: any;
  videoId?: any;
  videoLink?: string;
  sessionId?: any;
  sessionInfo?: any;

  // App Only
  signatureDataUrl?: string;
}
export interface UserProfession {
  userId?: string;
  professionId: any;
  reaction?: string;
  reason?: string;
  action?: string;
  order?: number;
  createdAt: any;
  appState?: any;
  botExplanation?: any;
}
export interface UserSegment {
  userId?: string;
  segmentId: any;
  professionId: any;
  reaction?: string;
  reason?: string;
  order?: number;
  createdAt: any;
  appState?: any;
}
export interface UserDiscipline {
  userId?: string;
  disciplineId: any;
  reaction?: string;
  reason?: string;
  action?: string;
  order?: number;
  createdAt: any;
  appState?: any;
  botExplanation?: any;
}
export interface UserElective {
  userId?: string;
  electiveId: any;
  reaction?: string;
  reason?: string;
  order?: number;
  createdAt: any;
  appState?: any;
  programId?: any;
}
export interface UserProgram {
  userId?: string;
  programId: any;
  reaction?: string;
  reason?: string;
  order?: number;
  createdAt: any;
  appState?: any;
  disciplineId?: any;
  botExplanation?: any;
}
export interface UserUser {
  userId?: string;
  targetUserId: string;
  sessionId?: any;
  reaction?: string;
  reason?: string;
  order?: number;
  createdAt: any;
  appState?: any;
  grade?: any;
}

// Elective
export interface Elective {
  id: any;
  name: string;
  groupName: string;
  shortName: string;
  type: string;
  reason?: string; // for UserElective
  isPreferred?: boolean;
  isRequired?: boolean;
}

// Subject
export interface Subject {
  id: any;
  name: string;
  shortName: string;
  type: string;
}

// Nominee
export interface Nominee {
  id?: any;
  fullName?: string;
  class?: string;
  phone?: string;
  schoolId?: string;
  isNewStudent?: boolean;
}

// University Data
export interface Discipline {
  id: any;
  name: string;
  nameChi: string;
  disciplineGroupId: number;
  groupName: string;
  groupNameChi: string;
  reason?: string; // for UserDiscipline
  action?: string;
  jupasProgramCount?: any;

  // Used in App only
  selectedPrograms?: Program[];
  explanation?: string; // generated by GPT (AB3 AI)
}
export interface DisciplineGroup {
  id: any;
  name: string;
  nameChi: string;
  disciplines: Discipline[];
  groupedNumOfA1B3: any;
  jupasProgramCount?: any;
}
export interface Program {
  id: any;
  name: string;
  displayName: string;
  status: any;
  sortOrder: any; // for identifying featured programs (highlight)

  nameChi?: string;
  nameShort?: string;
  programType?: string;
  jupasUrl?: string;
  jupasCode?: string;

  institutionId: string;
  institutionNameShort?: string;

  cspeProgramUrl: string;
  programWebsite: string;

  disciplines: Discipline[];

  requiredElective1: any[];
  requiredElective2: any[];
  requiredElective3: any[];
  preferredSubjects: any[];

  // Used in App only
  selectedElectives?: Elective[];
  mainDisciplineGroupName?: string;
  mainDisciplineGroupId?: any;

  // JUPAS only
  numOfPlaces?: any;
  best5Score: any;
  interviewArrangements?: any; // Yes (on a selective basis)
  admissions?: ProgramAdmission[];

  // For Score Calculation
  scoreRules?: ProgramScoreRule[];
  subjectRules?: ProgramSubjectRule[];

  // Mega Program List
  numOfA1B3?: any; // number of students chose the program (A1-B3)
  groupedNumOfA1B3?: any; // grouped by year of DSE
  scoreMedian?: any;
  avgStudentScore?: any; // avg. predicted scores of students selected the program
}
export interface ProgramAdmission {
  intakeYear: string; // important for correct score calculation in comparison
  id: any;
  programId: any;
  scoreMedian: any;
  scoreLowerQuartile: any;
  scoreMean: any; // mainly for HKBU
  interviewArrangements?: any; // Yes (on a selective basis)
  numOfPlaces: any;
  numOfBandAOffers: any;
  bandACompRatio: any;
}
export interface ProgramSubjectRule {
  intakeYear: any; // important for correct score calculation in comparison
  programId?: any;
  subjectId: any;
  minGrade: string;
  weighting: number;
}
export interface ProgramScoreRule {
  intakeYear: any; // important for correct score calculation in comparison
  programId?: any;
  subject1: any;
  subject2: any;
  subject3: any;
  subject4: any;
  subject5: any;
  subject6: any;
  subject7: any;
  levelU: number;
  level1: number;
  level2: number;
  level3: number;
  level4: number;
  level5: number;
  level5s: number;
  level5ss: number;
}

export interface Institution {
  id: any;
  name: string
  nameChi: string;
  nameShort: string;
  website?: string;
  jupasProgramCount?: any;
}

// Career Data
export interface Sector {
  id: any;
  name: string;
  extraInfoGroupId: number;
  extraInfo?: Object;
  fetchedExtraInfo?: boolean;
  likedByUser?: boolean;
}

export interface Segment {
  id: any;
  name: string;
  sectorId: string;
  extraInfoGroupId: number;
  sector?: Sector;
  likedByUser?: boolean;
  reason?: string; // for UserSegment only
}

export interface ProfessionTab {
  id: any;
  name: string;
  relatedProfessionIds: any;
}

export interface Profession {
  professionGroupName?: string;

  id: any;
  name: string;
  nameChinese: string;
  numOfCvBestExamples: number;

  extraInfoGroupId: number;
  extraInfo?: Object;
  fetchedExtraInfo?: boolean;
  imgLink?: string;

  requiresBachelor: boolean;
  requiresDoctorate: boolean;
  requiresHigherDiplomaAndAssociate: boolean;
  requiresMaster: boolean;
  requiresSecondarySchool: boolean;

  likedByUser?: boolean;
  isSelected?: boolean;

  relatedSegments?: Segment[];
  relatedTabIds: number[];
  relatedDisciplines: Discipline[];
  isProfessional: boolean; // Contain Discipline Group = 9 (Professional)

  userResponse: string;
  reason?: string;
  action?: string;

  imagePrompt: any; // for AI image competition

  // Used in App only
  selectedSegments?: Segment[];
  explanation?: string; // generated by GPT (SLP)
  explanationTraditionalChinese?: string; // generated by GPT (SLP)
  point1?: string; // generated by GPT (SLP)
  point2?: string; // generated by GPT (SLP)
  point3?: string; // generated by GPT (SLP)
  suitabilityScore?: number; // generated by GPT (SLP)
}

// Employer & Job
export interface Employer {
  id: string;
  name: string;
  companyUrl: string;
  shortName: string;

  jobs?: EmployerJob[];
}

export interface EmployerJob {
  id: string;
  employerId: string;
  position: string;
  positionUrl: string; // JD
  postDate: string;
  sessionId: string; // maybe only for students attended specific session]
  remarks: string; // extra links / description (linkify)

  employerName?: string;
  employerWebsite?: string;
}

export interface UserEmployerJob {
  userId?: string;
  employerJobId: any;
  reaction?: string;
  reason?: string;
  action?: string;
  order?: number;
  createdAt: any;
  appState?: any;
}

// Profssesor view
export interface Client {
  id: any;
  university?: string;
  department?: string;
  fullName?: string;
  salutation?: string;
  recipientList?: string;
  ccList?: string;
  tagline?: string; // e.g. ESG 原來咁樣幫到商學院面試，甚至攞到好GPA，搵好工!
  subtagline?: string; // e.g. {{related_subject}} ESG careers, subsequent events ...
  topic?: string; // e.g. ESG
  workServiceId?: string;
  workName?: string;
  jobEXName?: string;
  shortReportLink?: string;
  longReportLink?: string;
  reportId?: string;
  type?: string;
  whatsAppGroupId?: string;
  whatsAppGroupLink?: string;
  logoLink: string;
  bannerLink?: string;
  homeSlideLink?: string; // milestones / timeline etc.

  professionsByTags: ClientProfession[]; // professions by subject tags
  programRelatedProfessionIds: any[]; // e.g. [1, 2, 3]
  clientPhotos?: any[];

  // AchieveJUPAS
  programId?: any;
  allRelatedProgramIds: any[];
  competingProgramIds: any[];
  groupedBattleFieldNumOfA1B3?: any;
  battleFieldName?: string;
  targetDisciplineGroupId?: any;
  includeAchieveJUPASData?: boolean;

  // Home page dashboard
  targetBands?: any[];
  clientSections?: ClientSection[];

  // Create winning features
  createStep1?: any;
  createStep2?: any;
  createStep3?: any;
  createStep4?: any;
  createStep5?: any;
  createStep6?: any;
  createStep7?: any;
  createStep8?: any;

  // Client view checklist items
  renewalMeetingInfo?: string; // e.g. Renewal proposal meeting Jun 20 16:00 @ PolyU GH501
}
export interface ClientSection {
  id: any;
  title?: string;
  group?: string;
  order?: number;
  items?: ClientSectionItem[];
  filteredItems?: ClientSectionItem[]; // app only
  numOfSchools?: number;
  numOfTeachers?: number;
  yearDSE?: any;
}
export interface ClientSectionItem {
  id?: any;
  title?: string;
  description?: string;
  number?: string;
  order?: number;
  yearDSE?: any;
  photo1Link?: string;
  photo2Link?: string;
  sessionId?: string;
}

export interface ClientProfession {
  id: any;
  clientId: any;
  tag: string;
  professionIds: string[];
  relatedSchoolRoles: string[];
}

export interface SchoolRole {
  title: string; // e.g. Principal / Career Teacher / Subject Teacher
  alias: string; // Careers / Chinese / ...
  relatedSubject: string; // e.g. Chinese / English / ...
}